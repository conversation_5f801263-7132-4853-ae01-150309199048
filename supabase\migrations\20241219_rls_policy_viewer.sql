-- Migration: RLS Policy Viewer
-- Description: Add function to query RLS policies for dashboard monitoring
-- Date: 2024-12-19

-- Create a secure function to query RLS policies
-- This function allows the dashboard to view RLS policy information
-- while maintaining security by only exposing read-only policy metadata

CREATE OR REPLACE FUNCTION execute_sql(query text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    result json;
    allowed_queries text[] := ARRAY[
        'SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check FROM pg_policies',
        'SELECT * FROM pg_policies'
    ];
    is_allowed boolean := false;
    clean_query text;
BEGIN
    -- Clean the query (remove extra whitespace, normalize)
    clean_query := trim(regexp_replace(query, '\s+', ' ', 'g'));
    
    -- Check if the query is in our allowed list or is a safe pg_policies query
    IF clean_query ILIKE 'SELECT % FROM pg_policies%' OR 
       clean_query ILIKE 'SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check FROM pg_policies%' THEN
        is_allowed := true;
    END IF;
    
    -- Additional safety check - ensure it's only SELECT statements on pg_policies
    IF NOT (clean_query ILIKE 'SELECT %' AND clean_query ILIKE '%pg_policies%') THEN
        is_allowed := false;
    END IF;
    
    -- Block any potentially dangerous operations
    IF clean_query ILIKE '%INSERT%' OR 
       clean_query ILIKE '%UPDATE%' OR 
       clean_query ILIKE '%DELETE%' OR 
       clean_query ILIKE '%DROP%' OR 
       clean_query ILIKE '%CREATE%' OR 
       clean_query ILIKE '%ALTER%' OR 
       clean_query ILIKE '%GRANT%' OR 
       clean_query ILIKE '%REVOKE%' THEN
        is_allowed := false;
    END IF;
    
    IF NOT is_allowed THEN
        RAISE EXCEPTION 'Query not allowed. Only SELECT queries on pg_policies are permitted.';
    END IF;
    
    -- Execute the query and return results as JSON
    EXECUTE 'SELECT json_agg(row_to_json(t)) FROM (' || clean_query || ') t' INTO result;
    
    -- Return empty array if no results
    IF result IS NULL THEN
        result := '[]'::json;
    END IF;
    
    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and return a safe error message
        RAISE EXCEPTION 'Error executing query: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION execute_sql(text) TO authenticated;

-- Create a more specific function for RLS policy queries
CREATE OR REPLACE FUNCTION get_rls_policies(table_filter text DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    result json;
    base_query text;
BEGIN
    base_query := 'SELECT 
        schemaname,
        tablename,
        policyname,
        permissive,
        roles,
        cmd,
        qual,
        with_check
    FROM pg_policies';
    
    -- Add table filter if provided
    IF table_filter IS NOT NULL AND table_filter != '' THEN
        base_query := base_query || ' WHERE tablename = ' || quote_literal(table_filter);
    END IF;
    
    base_query := base_query || ' ORDER BY schemaname, tablename, policyname';
    
    -- Execute the query and return results as JSON
    EXECUTE 'SELECT json_agg(row_to_json(t)) FROM (' || base_query || ') t' INTO result;
    
    -- Return empty array if no results
    IF result IS NULL THEN
        result := '[]'::json;
    END IF;
    
    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error fetching RLS policies: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_rls_policies(text) TO authenticated;

-- Create a function to get RLS policy summary statistics
CREATE OR REPLACE FUNCTION get_rls_policy_stats()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    result json;
BEGIN
    SELECT json_build_object(
        'total_policies', COUNT(*),
        'tables_with_rls', COUNT(DISTINCT tablename),
        'schemas_with_rls', COUNT(DISTINCT schemaname),
        'policy_types', json_object_agg(cmd, cmd_count)
    ) INTO result
    FROM (
        SELECT 
            cmd,
            COUNT(*) as cmd_count
        FROM pg_policies 
        GROUP BY cmd
    ) policy_counts,
    pg_policies;
    
    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error fetching RLS policy statistics: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_rls_policy_stats() TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION execute_sql(text) IS 'Secure function to execute read-only queries on pg_policies view for dashboard monitoring';
COMMENT ON FUNCTION get_rls_policies(text) IS 'Get RLS policies with optional table filtering for dashboard display';
COMMENT ON FUNCTION get_rls_policy_stats() IS 'Get summary statistics about RLS policies for dashboard metrics';

-- Create a view for easier policy access (optional)
CREATE OR REPLACE VIEW dashboard_rls_policies AS
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check,
    CASE 
        WHEN cmd = 'ALL' THEN 'Full Access'
        WHEN cmd = 'SELECT' THEN 'Read Only'
        WHEN cmd = 'INSERT' THEN 'Insert Only'
        WHEN cmd = 'UPDATE' THEN 'Update Only'
        WHEN cmd = 'DELETE' THEN 'Delete Only'
        ELSE cmd
    END as policy_type_description,
    CASE 
        WHEN permissive = 'PERMISSIVE' THEN 'Allows access'
        WHEN permissive = 'RESTRICTIVE' THEN 'Restricts access'
        ELSE permissive
    END as permissive_description
FROM pg_policies
ORDER BY schemaname, tablename, policyname;

-- Grant select permission on the view to authenticated users
GRANT SELECT ON dashboard_rls_policies TO authenticated;

COMMENT ON VIEW dashboard_rls_policies IS 'Dashboard-friendly view of RLS policies with human-readable descriptions';
