# aiService.ts

**Path**: `src/services/aiService.ts`  
**Type**: Service Module  
**Lines**: 235  
**Author**: -

## Overview

Core AI service implementing a dual-agent system for code transformation. Features a planner agent that generates improvements and a critic agent that evaluates and scores the changes.

## Interfaces

### `AgentResponse`
- **Lines**: 2-8
- **Description**: Defines the structure for agent responses
- **Properties**:
  - `agent`: 'planner' | 'critic'
  - `message`: string
  - `score?`: number
  - `patch?`: string
  - `approved?`: boolean

### `TransformationResult`
- **Lines**: 10-16
- **Description**: Defines the final transformation result structure
- **Properties**:
  - `originalCode`: string
  - `transformedCode`: string
  - `diff`: string
  - `iterations`: number
  - `finalScore`: number

## Main Class

### `AIService`
- **Lines**: 18-234
- **Type**: Exported class
- **Description**: Main service class implementing dual-agent code transformation

#### Constants
- **`SCORE_THRESHOLD`**: 0.9 - Minimum score for approval
- **`MAX_ITERATIONS`**: 5 - Maximum transformation iterations

#### Public Methods

##### `transformCode(code: string, onLog: Function): Promise<TransformationResult>`
- **Lines**: 22-103
- **Description**: Main transformation method implementing the dual-agent loop
- **Parameters**:
  - `code`: Source code to transform
  - `onLog`: Callback function for logging events
- **Returns**: Promise resolving to TransformationResult

**Algorithm**:
1. Initialize iteration counter and score tracking
2. Loop until max iterations or score threshold reached:
   - Call planner agent to generate improvements
   - Call critic agent to evaluate changes
   - Check approval and score criteria
   - Apply changes if approved
3. Generate diff and return results

#### Private Methods

##### `callPlannerAgent(code: string): Promise<AgentResponse>`
- **Lines**: 105-117
- **Description**: Simulates planner agent API call
- **Logic**: Analyzes code for improvements and generates patches

##### `callCriticAgent(originalCode: string, patch?: string): Promise<AgentResponse>`
- **Lines**: 119-146
- **Description**: Simulates critic agent evaluation
- **Logic**: Evaluates patches and provides scores/approval

##### `analyzeCodeForImprovements(code: string): string[]`
- **Lines**: 148-172
- **Description**: Identifies potential code improvements
- **Checks**:
  - Recursive functions without memoization
  - Inefficient loops
  - Missing error handling

##### `generatePatch(originalCode: string, improvements: string[]): string`
- **Lines**: 174-194
- **Description**: Creates code patches based on identified improvements

##### `evaluateCode(code: string): number`
- **Lines**: 200-211
- **Description**: Scores code quality (0-1 scale)
- **Factors**:
  - Memoization usage (+0.3)
  - Modern syntax (+0.1)
  - Arrow functions (+0.1)
  - Error handling (+0.1)

##### `generateDiff(original: string, modified: string): string`
- **Lines**: 213-229
- **Description**: Creates unified diff format output

##### `delay(ms: number): Promise<void>`
- **Lines**: 231-233
- **Description**: Utility for simulating async delays

## Usage Example

```typescript
const aiService = new AIService();

const result = await aiService.transformCode(
  sourceCode,
  (logEntry) => console.log(logEntry)
);

console.log(`Transformed in ${result.iterations} iterations`);
console.log(`Final score: ${result.finalScore}`);
```

## Architecture

The service implements a **dual-agent architecture**:

1. **Planner Agent**: Analyzes code and proposes improvements
2. **Critic Agent**: Evaluates proposals and provides quality scores
3. **Iterative Refinement**: Continues until quality threshold met or max iterations reached

## Improvement Detection

The service can detect and fix:
- Recursive functions without memoization
- Inefficient array operations
- Missing error handling for JSON parsing
- Outdated JavaScript syntax

## Quality Scoring

Code quality is evaluated based on:
- **Memoization**: Efficient recursive algorithms
- **Modern Syntax**: const/let usage, arrow functions
- **Error Handling**: try/catch blocks
- **Base Score**: 0.5 starting point

## Related Files

- **Used by**: Dashboard component, transformation hooks
- **Dependencies**: None (self-contained service)

---

*Part of the Code Alchemy Reactor source catalog*
