import { chromium } from 'playwright';
import AxeBuilder from '@axe-core/playwright';
import fs from 'fs';
import path from 'path';

/**
 * Simple accessibility and responsive design checker
 * Takes screenshots at required breakpoints and runs basic axe audit
 */

async function runAccessibilityCheck() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  const results = {
    accessibility: {},
    screenshots: [],
    timestamp: new Date().toISOString()
  };

  try {
    // Navigate to dashboard
    console.log('Navigating to dashboard...');
    await page.goto('http://localhost:8080/dashboard');
    await page.waitForSelector('main', { timeout: 10000 });
    
    console.log('Dashboard loaded successfully');

    // Test responsive breakpoints
    const breakpoints = [
      { name: 'Mobile', width: 320, height: 568 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1280, height: 720 }
    ];

    for (const breakpoint of breakpoints) {
      console.log(`Testing ${breakpoint.name} (${breakpoint.width}x${breakpoint.height})`);
      
      // Set viewport
      await page.setViewportSize({ 
        width: breakpoint.width, 
        height: breakpoint.height 
      });
      
      // Wait for layout to settle
      await page.waitForTimeout(1000);
      
      // Take screenshot
      const screenshotPath = `e2e/screenshots/accessibility-${breakpoint.name.toLowerCase()}-${breakpoint.width}x${breakpoint.height}.png`;
      await page.screenshot({ 
        path: screenshotPath,
        fullPage: true 
      });
      
      console.log(`Screenshot saved: ${screenshotPath}`);
      results.screenshots.push({
        breakpoint: breakpoint.name,
        dimensions: `${breakpoint.width}x${breakpoint.height}`,
        path: screenshotPath
      });

      // Run basic accessibility check at this breakpoint
      try {
        console.log(`Running accessibility audit for ${breakpoint.name}...`);
        const accessibilityResults = await new AxeBuilder({ page })
          .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
          .analyze();

        const criticalViolations = accessibilityResults.violations.filter(v => v.impact === 'critical').length;
        const seriousViolations = accessibilityResults.violations.filter(v => v.impact === 'serious').length;
        const moderateViolations = accessibilityResults.violations.filter(v => v.impact === 'moderate').length;
        const minorViolations = accessibilityResults.violations.filter(v => v.impact === 'minor').length;

        // Calculate score (100 - weighted violations)
        const score = Math.max(0, 100 - (criticalViolations * 10 + seriousViolations * 5 + moderateViolations * 2 + minorViolations * 1));

        results.accessibility[breakpoint.name] = {
          score,
          violations: {
            critical: criticalViolations,
            serious: seriousViolations,
            moderate: moderateViolations,
            minor: minorViolations,
            total: accessibilityResults.violations.length
          },
          details: accessibilityResults.violations.map(v => ({
            id: v.id,
            impact: v.impact,
            description: v.description,
            help: v.help,
            nodes: v.nodes.length
          }))
        };

        console.log(`${breakpoint.name} Accessibility Score: ${score}/100`);
        console.log(`  Critical: ${criticalViolations}, Serious: ${seriousViolations}, Moderate: ${moderateViolations}, Minor: ${minorViolations}`);

      } catch (axeError) {
        console.error(`Accessibility check failed for ${breakpoint.name}:`, axeError.message);
        results.accessibility[breakpoint.name] = {
          error: axeError.message
        };
      }
    }

    // Test focus indicators
    console.log('Testing focus indicators...');
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Test focus on buttons
    const buttons = await page.locator('button').all();
    let focusTestResults = [];
    
    for (let i = 0; i < Math.min(buttons.length, 5); i++) {
      const button = buttons[i];
      try {
        if (await button.isVisible() && await button.isEnabled()) {
          await button.focus();
          const isFocused = await button.evaluate(el => document.activeElement === el);
          const hasVisibleFocus = await button.evaluate(el => {
            const styles = window.getComputedStyle(el, ':focus-visible');
            return styles.outline !== 'none' || el.classList.contains('focus-enhanced');
          });
          
          focusTestResults.push({
            buttonIndex: i,
            focused: isFocused,
            visibleFocus: hasVisibleFocus
          });
        }
      } catch (err) {
        console.log(`Focus test failed for button ${i}:`, err.message);
      }
    }

    results.focusTests = focusTestResults;
    console.log(`Tested focus on ${focusTestResults.length} buttons`);

    // Save results to file
    const resultsPath = 'e2e/accessibility-results.json';
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    console.log(`Results saved to: ${resultsPath}`);

    // Print summary
    console.log('\n=== ACCESSIBILITY AUDIT SUMMARY ===');
    Object.entries(results.accessibility).forEach(([breakpoint, data]) => {
      if (data.error) {
        console.log(`${breakpoint}: ERROR - ${data.error}`);
      } else {
        console.log(`${breakpoint}: Score ${data.score}/100 (Target: ≥97)`);
        if (data.score < 97) {
          console.log(`  ⚠️  Below target! Violations: ${data.violations.total}`);
          data.details.forEach(violation => {
            console.log(`    - ${violation.id} (${violation.impact}): ${violation.description}`);
          });
        } else {
          console.log(`  ✅ Meets target!`);
        }
      }
    });

    console.log('\n=== RESPONSIVE DESIGN ===');
    results.screenshots.forEach(screenshot => {
      console.log(`✅ ${screenshot.breakpoint} (${screenshot.dimensions}): ${screenshot.path}`);
    });

    console.log('\n=== FOCUS INDICATORS ===');
    const focusedButtons = focusTestResults.filter(r => r.focused).length;
    const visibleFocusButtons = focusTestResults.filter(r => r.visibleFocus).length;
    console.log(`Focusable buttons: ${focusedButtons}/${focusTestResults.length}`);
    console.log(`Visible focus indicators: ${visibleFocusButtons}/${focusTestResults.length}`);

    // Overall assessment
    const overallScores = Object.values(results.accessibility)
      .filter(data => !data.error)
      .map(data => data.score);
    
    if (overallScores.length > 0) {
      const minScore = Math.min(...overallScores);
      const avgScore = overallScores.reduce((a, b) => a + b, 0) / overallScores.length;
      
      console.log('\n=== OVERALL ASSESSMENT ===');
      console.log(`Minimum Score: ${minScore}/100`);
      console.log(`Average Score: ${avgScore.toFixed(1)}/100`);
      console.log(`Target Met: ${minScore >= 97 ? '✅ YES' : '❌ NO'}`);
      
      if (minScore < 97) {
        console.log('\n🔧 RECOMMENDATIONS:');
        console.log('- Fix critical and serious accessibility violations');
        console.log('- Ensure all interactive elements have visible focus indicators');
        console.log('- Test with screen readers and keyboard navigation');
        console.log('- Verify color contrast ratios meet WCAG 2.2 standards');
      }
    }

  } catch (error) {
    console.error('Accessibility check failed:', error);
    results.error = error.message;
  } finally {
    await browser.close();
  }

  return results;
}

// Run the check
runAccessibilityCheck()
  .then(() => {
    console.log('\nAccessibility check completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to run accessibility check:', error);
    process.exit(1);
  });

export { runAccessibilityCheck };
