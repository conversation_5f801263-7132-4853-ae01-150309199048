import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  DollarSign, 
  TrendingUp, 
  AlertTriangle, 
  Clock,
  Activity,
  RefreshCw,
  CreditCard,
  Target,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';

interface UsageSummary {
  current_period: {
    start: string;
    end: string;
  };
  usage_by_type: Record<string, number>;
  total_usage: number;
  limit: number | null;
  remaining: number | null;
}

interface CostData {
  currentCost: number;
  dailyBudget: number;
  monthlyBudget: number;
  costToday: number;
  costThisMonth: number;
  costPerTransformation: number;
  projectedMonthlyCost: number;
  usageSummary: UsageSummary | null;
}

interface CostTrackingWidgetProps {
  className?: string;
  refreshInterval?: number;
}

export const CostTrackingWidget: React.FC<CostTrackingWidgetProps> = ({
  className,
  refreshInterval = 30000 // 30 seconds
}) => {
  const [costData, setCostData] = useState<CostData>({
    currentCost: 0,
    dailyBudget: 10.0,
    monthlyBudget: 100.0,
    costToday: 0,
    costThisMonth: 0,
    costPerTransformation: 0,
    projectedMonthlyCost: 0,
    usageSummary: null
  });
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [error, setError] = useState<string | null>(null);

  // Fetch cost and usage data
  const fetchCostData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch usage summary
      const usageResponse = await fetch('/api/billing/usage');
      let usageSummary: UsageSummary | null = null;
      
      if (usageResponse.ok) {
        const usageResult = await usageResponse.json();
        usageSummary = usageResult.data;
      }

      // Fetch analytics data for cost information
      const analyticsResponse = await fetch('/api/analytics/usage?period=30d');
      let analyticsData = null;
      
      if (analyticsResponse.ok) {
        const analyticsResult = await analyticsResponse.json();
        analyticsData = analyticsResult;
      }

      // Calculate cost metrics
      const totalCost = analyticsData?.totalCost || 0;
      const averageCost = analyticsData?.averageCost || 0;
      const totalSessions = analyticsData?.totalSessions || 0;
      
      // Estimate daily and monthly costs
      const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
      const dayOfMonth = new Date().getDate();
      const costToday = totalCost / Math.max(1, dayOfMonth); // Rough estimate
      const projectedMonthlyCost = (costToday * daysInMonth);

      setCostData({
        currentCost: 0, // Current transformation cost (would be set during active transformation)
        dailyBudget: 10.0, // Default budget - could be fetched from user settings
        monthlyBudget: 100.0, // Default budget - could be fetched from user settings
        costToday,
        costThisMonth: totalCost,
        costPerTransformation: averageCost,
        projectedMonthlyCost,
        usageSummary
      });

    } catch (error) {
      console.error('Failed to fetch cost data:', error);
      setError('Failed to load cost data');
    } finally {
      setIsLoading(false);
      setLastUpdated(new Date());
    }
  };

  // Auto-refresh cost data
  useEffect(() => {
    fetchCostData();
    const interval = setInterval(fetchCostData, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(amount);
  };

  const getDaysRemainingInMonth = (): number => {
    const now = new Date();
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    return lastDay.getDate() - now.getDate();
  };

  const getUsagePercentage = (): number => {
    if (!costData.usageSummary?.limit) return 0;
    return (costData.usageSummary.total_usage / costData.usageSummary.limit) * 100;
  };

  const getDailyBudgetPercentage = (): number => {
    return (costData.costToday / costData.dailyBudget) * 100;
  };

  const getMonthlyBudgetPercentage = (): number => {
    return (costData.costThisMonth / costData.monthlyBudget) * 100;
  };

  const isNearDailyLimit = getDailyBudgetPercentage() > 80;
  const isNearMonthlyLimit = getMonthlyBudgetPercentage() > 80;
  const isNearUsageLimit = getUsagePercentage() > 80;

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <DollarSign className="w-5 h-5 text-green-500" />
            <CardTitle className="text-lg">Cost Tracking</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            {(isNearDailyLimit || isNearMonthlyLimit || isNearUsageLimit) && (
              <AlertTriangle className="w-4 h-4 text-yellow-500" />
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchCostData}
              disabled={isLoading}
              className="h-8 w-8 p-0 focus-enhanced"
              data-testid="cost-refresh-button"
              aria-label="Refresh cost tracking data"
            >
              <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
        <CardDescription>
          Real-time cost monitoring and budget tracking
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <Alert className="border-red-500/30 bg-red-900/10">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-200">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Current Period Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Activity className="w-4 h-4 text-blue-500" />
              <Typography variant="caption" className="text-muted-foreground">
                Today's Spend
              </Typography>
            </div>
            <Typography variant="h6" className="font-mono">
              {formatCurrency(costData.costToday)}
            </Typography>
            <div className={cn("text-xs", isNearDailyLimit ? 'text-yellow-500' : 'text-muted-foreground')}>
              of {formatCurrency(costData.dailyBudget)} budget
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-purple-500" />
              <Typography variant="caption" className="text-muted-foreground">
                This Month
              </Typography>
            </div>
            <Typography variant="h6" className="font-mono">
              {formatCurrency(costData.costThisMonth)}
            </Typography>
            <div className={cn("text-xs", isNearMonthlyLimit ? 'text-yellow-500' : 'text-muted-foreground')}>
              of {formatCurrency(costData.monthlyBudget)} budget
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Target className="w-4 h-4 text-orange-500" />
              <Typography variant="caption" className="text-muted-foreground">
                Avg per Transform
              </Typography>
            </div>
            <Typography variant="h6" className="font-mono">
              {formatCurrency(costData.costPerTransformation)}
            </Typography>
            <div className="text-xs text-muted-foreground">
              per transformation
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-green-500" />
              <Typography variant="caption" className="text-muted-foreground">
                Projected Month
              </Typography>
            </div>
            <Typography variant="h6" className="font-mono">
              {formatCurrency(costData.projectedMonthlyCost)}
            </Typography>
            <div className="text-xs text-muted-foreground">
              estimated total
            </div>
          </div>
        </div>

        {/* Budget Progress */}
        <div className="space-y-4">
          <Typography variant="body-sm" className="font-medium">
            Budget Usage
          </Typography>
          
          {/* Daily Budget */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Typography variant="caption" className="text-muted-foreground">
                Daily Budget
              </Typography>
              <Badge variant={isNearDailyLimit ? 'destructive' : 'secondary'}>
                {getDailyBudgetPercentage().toFixed(1)}%
              </Badge>
            </div>
            <Progress 
              value={getDailyBudgetPercentage()} 
              className={cn("h-2", isNearDailyLimit && "bg-red-900")}
            />
          </div>

          {/* Monthly Budget */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Typography variant="caption" className="text-muted-foreground">
                Monthly Budget
              </Typography>
              <Badge variant={isNearMonthlyLimit ? 'destructive' : 'secondary'}>
                {getMonthlyBudgetPercentage().toFixed(1)}%
              </Badge>
            </div>
            <Progress 
              value={getMonthlyBudgetPercentage()} 
              className={cn("h-2", isNearMonthlyLimit && "bg-red-900")}
            />
          </div>
        </div>

        {/* Usage Limits */}
        {costData.usageSummary && costData.usageSummary.limit && (
          <div className="space-y-3">
            <Typography variant="body-sm" className="font-medium">
              Usage Limits
            </Typography>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Typography variant="caption" className="text-muted-foreground">
                  Transformations Used
                </Typography>
                <Badge variant={isNearUsageLimit ? 'destructive' : 'secondary'}>
                  {costData.usageSummary.total_usage} / {costData.usageSummary.limit}
                </Badge>
              </div>
              <Progress 
                value={getUsagePercentage()} 
                className={cn("h-2", isNearUsageLimit && "bg-red-900")}
              />
              {costData.usageSummary.remaining !== null && (
                <Typography variant="caption" className="text-muted-foreground">
                  {costData.usageSummary.remaining} transformations remaining this period
                </Typography>
              )}
            </div>
          </div>
        )}

        {/* Alerts */}
        {(isNearDailyLimit || isNearMonthlyLimit || isNearUsageLimit) && (
          <Alert className="border-yellow-500/30 bg-yellow-900/10">
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
            <AlertDescription className="text-yellow-200">
              <VStack spacing="xs">
                {isNearDailyLimit && <span>• Approaching daily budget limit</span>}
                {isNearMonthlyLimit && <span>• Approaching monthly budget limit</span>}
                {isNearUsageLimit && <span>• Approaching usage limit for current period</span>}
              </VStack>
            </AlertDescription>
          </Alert>
        )}

        {/* Period Info */}
        {costData.usageSummary?.current_period && (
          <div className="pt-2 border-t border-border">
            <Typography variant="caption" className="text-muted-foreground">
              Billing period: {new Date(costData.usageSummary.current_period.start).toLocaleDateString()} - {new Date(costData.usageSummary.current_period.end).toLocaleDateString()}
            </Typography>
            <br />
            <Typography variant="caption" className="text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </Typography>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CostTrackingWidget;
