import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import {
  Monitor,
  Activity,
  CreditCard,
  FileText,
  Settings,
  Users,
  BarChart3,
  Shield,
  Zap,
  Database,
  ArrowLeft,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';

// Import dashboard components
import DashboardIntegration from '@/components/dashboard/DashboardIntegration';

// Simple WebSocket connection status enum
enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
}

interface SystemMetrics {
  overall_health: 'healthy' | 'degraded' | 'down';
  uptime: number;
  active_services: number;
  total_services: number;
  avg_response_time: number;
  success_rate: number;
  queue_length: number;
  cost_this_month: number;
  api_calls: number;
  budget_used_percentage: number;
  errors_24h: number;
  warnings_24h: number;
  last_deploy: string;
}

const MonitoringDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [metrics, setMetrics] = useState<SystemMetrics>({
    overall_health: 'healthy',
    uptime: 99.9,
    active_services: 5,
    total_services: 5,
    avg_response_time: 45,
    success_rate: 99.8,
    queue_length: 3,
    cost_this_month: 12.45,
    api_calls: 1234,
    budget_used_percentage: 24,
    errors_24h: 2,
    warnings_24h: 8,
    last_deploy: '2h ago'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(ConnectionStatus.DISCONNECTED);
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);

  // Fetch real metrics from API
  const fetchMetrics = async () => {
    setIsLoading(true);
    try {
      // Try to fetch from multiple endpoints
      const [healthResponse, logsResponse, aiResponse, queueResponse] = await Promise.allSettled([
        fetch('/health'),
        fetch('/api/logs/metrics?timeRange=24h'),
        fetch('/api/ai/performance?hours=24'),
        fetch('/api/queue/status')
      ]);

      // Process health data
      let healthData = null;
      if (healthResponse.status === 'fulfilled' && healthResponse.value.ok) {
        healthData = await healthResponse.value.json();
      }

      // Process logs data
      let logsData = null;
      if (logsResponse.status === 'fulfilled' && logsResponse.value.ok) {
        const logsResult = await logsResponse.value.json();
        logsData = logsResult.data;
      }

      // Process AI performance data
      let aiData = null;
      if (aiResponse.status === 'fulfilled' && aiResponse.value.ok) {
        const aiResult = await aiResponse.value.json();
        aiData = aiResult;
      }

      // Process queue data
      let queueData = null;
      if (queueResponse.status === 'fulfilled' && queueResponse.value.ok) {
        const queueResult = await queueResponse.value.json();
        queueData = queueResult.data;
      }

      // Calculate metrics from real data
      const calculateErrorsAndWarnings = (logs: any) => {
        if (!logs || !logs.error_rate) return { errors: 0, warnings: 0 };

        const errors = logs.error_rate.filter((log: any) => log.level === 'error').length;
        const warnings = logs.error_rate.filter((log: any) => log.level === 'warn').length;
        return { errors, warnings };
      };

      const calculatePerformanceMetrics = (aiPerf: any) => {
        if (!aiPerf || !aiPerf.performance) return { avgResponseTime: 45, successRate: 99.8 };

        const performance = aiPerf.performance;
        const avgResponseTime = performance.reduce((acc: number, p: any) => acc + (p.avg_response_time || 0), 0) / performance.length || 45;
        const successRate = performance.reduce((acc: number, p: any) => acc + (p.success_rate || 0), 0) / performance.length || 99.8;

        return { avgResponseTime: Math.round(avgResponseTime), successRate: Math.round(successRate * 10) / 10 };
      };

      const calculateCostMetrics = (logs: any) => {
        if (!logs || !logs.cost_metrics) return { costThisMonth: 12.45, apiCalls: 1234 };

        const costMetrics = logs.cost_metrics;
        const totalCost = costMetrics.reduce((acc: number, c: any) => acc + (c.total_cost || 0), 0);
        const totalCalls = costMetrics.reduce((acc: number, c: any) => acc + (c.request_count || 0), 0);

        return {
          costThisMonth: Math.round(totalCost * 100) / 100,
          apiCalls: totalCalls
        };
      };

      const { errors, warnings } = calculateErrorsAndWarnings(logsData);
      const { avgResponseTime, successRate } = calculatePerformanceMetrics(aiData);
      const { costThisMonth, apiCalls } = calculateCostMetrics(logsData);

      // Update metrics with real data where available
      setMetrics(prev => ({
        ...prev,
        overall_health: healthData?.status === 'ok' ? 'healthy' : (healthData ? 'degraded' : prev.overall_health),
        avg_response_time: avgResponseTime,
        success_rate: successRate,
        queue_length: queueData?.queueLength || prev.queue_length,
        cost_this_month: costThisMonth,
        api_calls: apiCalls,
        budget_used_percentage: Math.round((costThisMonth / 100) * 100), // Assuming $100 budget
        errors_24h: errors,
        warnings_24h: warnings,
        active_services: aiData?.currentStatus?.filter((s: any) => s.available).length || prev.active_services,
        total_services: aiData?.currentStatus?.length || prev.total_services,
      }));

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch metrics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Simple WebSocket connection for real-time updates
  useEffect(() => {
    let ws: WebSocket | null = null;
    let reconnectTimer: NodeJS.Timeout | null = null;

    const connectWebSocket = () => {
      if (!realTimeEnabled) return;

      setConnectionStatus(ConnectionStatus.CONNECTING);

      try {
        // Connect to WebSocket server (adjust URL as needed)
        const wsUrl = `ws://localhost:3001`;
        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          setConnectionStatus(ConnectionStatus.CONNECTED);
          console.log('WebSocket connected for real-time monitoring');
        };

        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            // Update metrics with real-time data
            if (data.type === 'metrics_update') {
              setMetrics(prev => ({
                ...prev,
                ...data.metrics,
              }));
              setLastUpdated(new Date());
            }
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        ws.onclose = () => {
          setConnectionStatus(ConnectionStatus.DISCONNECTED);
          console.log('WebSocket disconnected');

          // Auto-reconnect after 5 seconds
          if (realTimeEnabled) {
            reconnectTimer = setTimeout(connectWebSocket, 5000);
          }
        };

        ws.onerror = (error) => {
          setConnectionStatus(ConnectionStatus.ERROR);
          console.error('WebSocket error:', error);
        };

      } catch (error) {
        setConnectionStatus(ConnectionStatus.ERROR);
        console.error('Failed to create WebSocket connection:', error);
      }
    };

    if (realTimeEnabled) {
      connectWebSocket();
    } else {
      setConnectionStatus(ConnectionStatus.DISCONNECTED);
    }

    return () => {
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
      }
      if (ws) {
        ws.close();
      }
    };
  }, [realTimeEnabled]);

  // Auto-refresh metrics every 30 seconds (fallback when real-time is disabled)
  useEffect(() => {
    fetchMetrics();

    if (!realTimeEnabled) {
      const interval = setInterval(fetchMetrics, 30000);
      return () => clearInterval(interval);
    }
  }, [realTimeEnabled]);

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-500';
      case 'degraded': return 'text-yellow-500';
      case 'down': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getHealthBadgeVariant = (health: string) => {
    switch (health) {
      case 'healthy': return 'default';
      case 'degraded': return 'secondary';
      case 'down': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/dashboard')}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
            <div className="flex items-center space-x-2">
              <Monitor className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold">Monitoring Dashboard</h1>
            </div>
            <Badge variant="secondary" className="bg-primary/20 text-primary border-primary/30">
              Admin
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <div className={cn("w-2 h-2 rounded-full",
                metrics.overall_health === 'healthy' ? 'bg-green-500' :
                metrics.overall_health === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
              )}></div>
              <span className="text-xs text-muted-foreground">
                {metrics.overall_health === 'healthy' ? 'All Systems Operational' :
                 metrics.overall_health === 'degraded' ? 'Some Issues Detected' : 'System Down'}
              </span>
            </div>

            {/* Real-time connection status */}
            <div className="flex items-center space-x-1">
              <div className={cn("w-2 h-2 rounded-full",
                connectionStatus === ConnectionStatus.CONNECTED ? 'bg-green-500 animate-pulse' :
                connectionStatus === ConnectionStatus.CONNECTING ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'
              )}></div>
              <span className="text-xs text-muted-foreground">
                {connectionStatus === ConnectionStatus.CONNECTED ? 'Live' :
                 connectionStatus === ConnectionStatus.CONNECTING ? 'Connecting' : 'Offline'}
              </span>
            </div>

            {/* Real-time toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setRealTimeEnabled(!realTimeEnabled)}
              className={cn("h-8 px-2 text-xs", realTimeEnabled ? "text-green-600" : "text-muted-foreground")}
            >
              {realTimeEnabled ? <Activity className="w-3 h-3 mr-1" /> : <Clock className="w-3 h-3 mr-1" />}
              {realTimeEnabled ? 'Live' : 'Manual'}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={fetchMetrics}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="health" className="flex items-center space-x-2">
              <Activity className="w-4 h-4" />
              <span>Health</span>
            </TabsTrigger>
            <TabsTrigger value="billing" className="flex items-center space-x-2">
              <CreditCard className="w-4 h-4" />
              <span>Billing</span>
            </TabsTrigger>
            <TabsTrigger value="logs" className="flex items-center space-x-2">
              <FileText className="w-4 h-4" />
              <span>Logs</span>
            </TabsTrigger>
            <TabsTrigger value="queue" className="flex items-center space-x-2">
              <Zap className="w-4 h-4" />
              <span>Queue</span>
            </TabsTrigger>
            <TabsTrigger value="secrets" className="flex items-center space-x-2">
              <Shield className="w-4 h-4" />
              <span>Secrets</span>
            </TabsTrigger>
            <TabsTrigger value="admin" className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span>Admin</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab - Main Dashboard */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* System Status Card */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Activity className="w-4 h-4" />
                    <span>System Status</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Overall Health</span>
                      <Badge variant={getHealthBadgeVariant(metrics.overall_health) as any}>
                        {metrics.overall_health.charAt(0).toUpperCase() + metrics.overall_health.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Uptime</span>
                      <span className="text-sm font-medium">{metrics.uptime}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Active Services</span>
                      <span className="text-sm font-medium">{metrics.active_services}/{metrics.total_services}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Performance Card */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Zap className="w-4 h-4" />
                    <span>Performance</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Avg Response</span>
                      <span className="text-sm font-medium">{metrics.avg_response_time}ms</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Success Rate</span>
                      <span className="text-sm font-medium text-green-600">{metrics.success_rate}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Queue Length</span>
                      <span className="text-sm font-medium">{metrics.queue_length}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Usage & Costs Card */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <CreditCard className="w-4 h-4" />
                    <span>Usage & Costs</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">This Month</span>
                      <span className="text-sm font-medium">${metrics.cost_this_month}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">API Calls</span>
                      <span className="text-sm font-medium">{metrics.api_calls.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Budget Used</span>
                      <span className="text-sm font-medium">{metrics.budget_used_percentage}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Activity Card */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <FileText className="w-4 h-4" />
                    <span>Recent Activity</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Errors (24h)</span>
                      <span className="text-sm font-medium">{metrics.errors_24h}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Warnings (24h)</span>
                      <span className="text-sm font-medium">{metrics.warnings_24h}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Last Deploy</span>
                      <span className="text-sm font-medium">{metrics.last_deploy}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Last Updated Info */}
            <div className="text-center text-sm text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </div>

            {/* Integrated Dashboard Components */}
            <div className="mt-8">
              <DashboardIntegration />
            </div>
          </TabsContent>

          {/* Dashboard Integration Tabs */}
          <TabsContent value="health" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>System Health Dashboard</CardTitle>
                <CardDescription>Detailed system health monitoring and diagnostics</CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Advanced system health monitoring is available in the Overview tab's integrated dashboard.
                    Navigate to the Overview tab and select the "Health" section for detailed metrics.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="billing" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Billing Dashboard</CardTitle>
                <CardDescription>Cost tracking and billing management</CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Billing and cost tracking features are available in the Overview tab's integrated dashboard.
                    Navigate to the Overview tab and select the "Analytics" section for detailed cost analysis.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="logs" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Logs Viewer</CardTitle>
                <CardDescription>System logs and error tracking</CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Advanced log viewing and analysis tools are available in the Overview tab's integrated dashboard.
                    Navigate to the Overview tab and select the "Admin" section for log management.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="queue" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Queue Management</CardTitle>
                <CardDescription>Job queue monitoring and management</CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Queue management and monitoring tools are available in the Overview tab's integrated dashboard.
                    Navigate to the Overview tab and select the "Admin" section for queue operations.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="secrets" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Secrets Management</CardTitle>
                <CardDescription>API keys and secrets configuration</CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    API key and secrets management is available in the Overview tab's integrated dashboard.
                    Navigate to the Overview tab and select the "Admin" section for secrets configuration.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="admin" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Admin Panel</CardTitle>
                <CardDescription>System administration and configuration</CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Complete admin panel functionality is available in the Overview tab's integrated dashboard.
                    Navigate to the Overview tab and select the "Admin" section for full administrative controls.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default MonitoringDashboard;
