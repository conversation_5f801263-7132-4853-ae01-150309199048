import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { InfoModal } from '@/components/ui/modal';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Shield, 
  ShieldCheck, 
  ShieldAlert, 
  ShieldX,
  RefreshCw,
  Copy,
  Eye,
  Lock
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { supabase } from '@/lib/supabase';

interface RLSPolicy {
  schemaname: string;
  tablename: string;
  policyname: string;
  permissive: string;
  roles: string[];
  cmd: string;
  qual: string;
  with_check: string;
}

interface RLSPolicyBadgeProps {
  className?: string;
  tableName?: string;
  showCount?: boolean;
}

export const RLSPolicyBadge: React.FC<RLSPolicyBadgeProps> = ({
  className,
  tableName,
  showCount = true
}) => {
  const [policies, setPolicies] = useState<RLSPolicy[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch RLS policies from Supabase
  const fetchRLSPolicies = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Use the dedicated RLS policy function
      const { data, error: queryError } = await supabase.rpc('get_rls_policies', {
        table_filter: tableName || null
      });

      if (queryError) {
        throw queryError;
      }

      setPolicies(data || []);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Failed to fetch RLS policies:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch RLS policies');
    } finally {
      setIsLoading(false);
    }
  };

  // Load policies on component mount
  useEffect(() => {
    fetchRLSPolicies();
  }, [tableName]);

  // Get badge variant based on policy status
  const getBadgeVariant = () => {
    if (error) return 'destructive';
    if (policies.length === 0) return 'secondary';
    return 'default';
  };

  // Get badge icon based on status
  const getBadgeIcon = () => {
    if (error) return <ShieldX className="w-3 h-3" />;
    if (policies.length === 0) return <ShieldAlert className="w-3 h-3" />;
    return <ShieldCheck className="w-3 h-3" />;
  };

  // Get badge text
  const getBadgeText = () => {
    if (error) return 'RLS Error';
    if (policies.length === 0) return 'No RLS';
    if (showCount) return `RLS (${policies.length})`;
    return 'RLS Active';
  };

  // Copy policy JSON to clipboard
  const copyPolicyToClipboard = async (policy: RLSPolicy) => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(policy, null, 2));
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  // Copy all policies to clipboard
  const copyAllPoliciesToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(policies, null, 2));
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  return (
    <>
      <Badge
        variant={getBadgeVariant()}
        className={cn(
          "cursor-pointer transition-all duration-200 hover:scale-105",
          "flex items-center space-x-1",
          className
        )}
        onClick={() => setIsModalOpen(true)}
        data-testid="rls-policy-badge"
        title="Click to view RLS policies"
      >
        {getBadgeIcon()}
        <span>{getBadgeText()}</span>
      </Badge>

      <InfoModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Row Level Security (RLS) Policies"
        size="xl"
      >
        <div className="space-y-4">
          {/* Header with refresh button */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Lock className="w-5 h-5 text-primary" />
              <span className="font-medium">
                {tableName ? `Policies for ${tableName}` : 'All RLS Policies'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {lastUpdated && (
                <span className="text-xs text-muted-foreground">
                  Updated: {lastUpdated.toLocaleTimeString()}
                </span>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={fetchRLSPolicies}
                disabled={isLoading}
                className="h-8"
              >
                <RefreshCw className={cn("w-4 h-4", isLoading && "animate-spin")} />
              </Button>
              {policies.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyAllPoliciesToClipboard}
                  className="h-8"
                  title="Copy all policies to clipboard"
                >
                  <Copy className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Error state */}
          {error && (
            <Alert variant="destructive">
              <ShieldX className="h-4 w-4" />
              <AlertDescription>
                Failed to load RLS policies: {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Loading state */}
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="w-6 h-6 animate-spin text-primary" />
              <span className="ml-2">Loading RLS policies...</span>
            </div>
          )}

          {/* No policies state */}
          {!isLoading && !error && policies.length === 0 && (
            <Alert>
              <ShieldAlert className="h-4 w-4" />
              <AlertDescription>
                No RLS policies found. This may indicate that Row Level Security is not enabled 
                or no policies have been configured.
              </AlertDescription>
            </Alert>
          )}

          {/* Policies list */}
          {!isLoading && !error && policies.length > 0 && (
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {policies.map((policy, index) => (
                  <div
                    key={`${policy.schemaname}.${policy.tablename}.${policy.policyname}`}
                    className="border rounded-lg p-4 space-y-3"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {policy.schemaname}.{policy.tablename}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {policy.cmd}
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyPolicyToClipboard(policy)}
                        className="h-6 w-6 p-0"
                        title="Copy policy to clipboard"
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                    </div>

                    <div>
                      <h4 className="font-medium text-sm">{policy.policyname}</h4>
                      <p className="text-xs text-muted-foreground">
                        Permissive: {policy.permissive} | Roles: {policy.roles?.join(', ') || 'None'}
                      </p>
                    </div>

                    {policy.qual && (
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">USING:</label>
                        <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-x-auto">
                          {policy.qual}
                        </pre>
                      </div>
                    )}

                    {policy.with_check && (
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">WITH CHECK:</label>
                        <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-x-auto">
                          {policy.with_check}
                        </pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}

          {/* Summary */}
          {!isLoading && !error && policies.length > 0 && (
            <div className="text-xs text-muted-foreground border-t pt-3">
              <p>
                Found {policies.length} RLS {policies.length === 1 ? 'policy' : 'policies'} 
                across {new Set(policies.map(p => `${p.schemaname}.${p.tablename}`)).size} tables.
              </p>
            </div>
          )}
        </div>
      </InfoModal>
    </>
  );
};

export default RLSPolicyBadge;
