{"config": {"configFile": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "artifacts/playwright-report.json"}], ["junit", {"outputFile": "artifacts/playwright-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.0", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:8080", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "accessibility-audit.spec.ts", "file": "accessibility-audit.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Accessibility Audit", "file": "accessibility-audit.spec.ts", "line": 11, "column": 6, "specs": [{"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 31627, "error": {"message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "snippet": "\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84\u001b[22m"}], "stdout": [{"text": "Accessibility violations: \u001b[33m2\u001b[39m\n"}, {"text": "Violations details:\n"}, {"text": "1. aria-valid-attr-value: Ensure all ARIA attributes have valid values\n"}, {"text": "   Impact: critical\n"}, {"text": "   Help: ARIA attributes must conform to valid values\n"}, {"text": "   Nodes: 2\n"}, {"text": "2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "   Impact: serious\n"}, {"text": "   Help: Elements must meet minimum color contrast ratio thresholds\n"}, {"text": "   Nodes: 15\n"}, {"text": "Accessibility Score: 85/100\n"}, {"text": "Critical: 1, Serious: 1, Moderate: 0, Minor: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:43:58.431Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-72eff16dad7ca79bd348", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 17, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1487\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1487\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1487\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:43:58.434Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-64f0146ac4ebba4f324a", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 18, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2182\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2182\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2182\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:43:58.554Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-6f4382caf52c8a0dffc0", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 27251, "error": {"message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "snippet": "\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84\u001b[22m"}], "stdout": [{"text": "Accessibility violations: \u001b[33m2\u001b[39m\n"}, {"text": "Violations details:\n"}, {"text": "1. button-name: Ensure buttons have discernible text\n"}, {"text": "   Impact: critical\n"}, {"text": "   Help: Buttons must have discernible text\n"}, {"text": "   Nodes: 2\n"}, {"text": "2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "   Impact: serious\n"}, {"text": "   Help: Elements must meet minimum color contrast ratio thresholds\n"}, {"text": "   Nodes: 9\n"}, {"text": "Accessibility Score: 85/100\n"}, {"text": "Critical: 1, Serious: 1, Moderate: 0, Minor: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:43:58.463Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-dd1b2834653f11d8d38f", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "failed", "duration": 141, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2182\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2182\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\webkit-2182\\Playwright.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:44:15.599Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-9825d36cf80d5b3420bc", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 5, "parallelIndex": 2, "status": "failed", "duration": 12211, "error": {"message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "snippet": "\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84\u001b[22m"}], "stdout": [{"text": "Accessibility violations: \u001b[33m2\u001b[39m\n"}, {"text": "Violations details:\n"}, {"text": "1. aria-valid-attr-value: Ensure all ARIA attributes have valid values\n"}, {"text": "   Impact: critical\n"}, {"text": "   Help: ARIA attributes must conform to valid values\n"}, {"text": "   Nodes: 2\n"}, {"text": "2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "   Impact: serious\n"}, {"text": "   Help: Elements must meet minimum color contrast ratio thresholds\n"}, {"text": "   Nodes: 15\n"}, {"text": "Accessibility Score: 85/100\n"}, {"text": "Critical: 1, Serious: 1, Moderate: 0, Minor: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-19T20:44:14.741Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-cbdd7d906870c899785e", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-19T20:43:52.835Z", "duration": 39462.137, "expected": 0, "skipped": 0, "unexpected": 6, "flaky": 0}}