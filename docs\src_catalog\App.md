# App.tsx

**Path**: `src/App.tsx`  
**Type**: React Component  
**Lines**: 34  
**Author**: gpt-engineer-app[bot]

## Overview

The main application component that sets up the React application structure with routing, providers, and global UI components.

## Symbols

### `queryClient`
- **Type**: Constant
- **Line**: 13
- **Description**: QueryClient instance for React Query state management

### `App`
- **Type**: React Functional Component
- **Lines**: 15-31
- **Description**: Main application component with routing setup
- **Export**: Default export

## Imports

```typescript
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import Settings from "./pages/Settings";
import History from "./pages/History";
import NotFound from "./pages/NotFound";
```

## Dependencies

- **React Query**: State management and caching
- **React Router**: Client-side routing
- **shadcn/ui**: Toast notifications and tooltips

## Routes

| Path | Component | Description |
|------|-----------|-------------|
| `/` | Index | Landing page |
| `/dashboard` | Dashboard | Main application interface |
| `/settings` | Settings | Configuration page |
| `/history` | History | Transformation history |
| `*` | NotFound | 404 error page |

## Architecture

The App component provides:

1. **Global Providers**:
   - QueryClientProvider for React Query
   - TooltipProvider for UI tooltips

2. **Global UI Components**:
   - Toaster for notifications
   - Sonner for toast messages

3. **Routing Setup**:
   - BrowserRouter for client-side routing
   - Route definitions for all pages

## Code Structure

```typescript
const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          {/* Route definitions */}
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);
```

## Usage

This component is imported and rendered in `main.tsx` as the root component of the React application.

## Related Files

- **Imported by**: `src/main.tsx`
- **Imports from**: All page components, UI components
- **Dependencies**: React Query, React Router, shadcn/ui components

---

*Part of the Code Alchemy Reactor source catalog*
