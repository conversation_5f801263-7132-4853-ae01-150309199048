
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import { Settings, History, ArrowLeft, RotateCcw, Download, BookOpen, Keyboard, Monitor, Code2, BarChart3, Shield, Users, Bell } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { CodeEditor } from "@/components/CodeEditor";
import { DiffViewer } from "@/components/DiffViewer";
import { AgentLog } from "@/components/AgentLog";
import { EnhancedEditor } from "@/components/editor/EnhancedEditor";
import { ControlPanel } from "@/components/ControlPanel";
import { CodeExamples } from "@/components/CodeExamples";
import { HelpPanel } from "@/components/HelpPanel";
import { useCodeTransformation } from "@/hooks/useCodeTransformation";
import { useTransformationHistory } from "@/hooks/useTransformationHistory";
import { useKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts";

// Import dashboard integration components
import DashboardIntegration from "@/components/dashboard/DashboardIntegration";
import { OnboardingWizard } from "@/components/app/onboarding/OnboardingWizard";

const Dashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentCode, setCurrentCode] = useState(`// Welcome to Metamorphic Reactor
// Paste your code here and let the dual-AI system improve it

function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10));`);

  const [showExamples, setShowExamples] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [activeTab, setActiveTab] = useState("code-editor");
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Enhanced editor state
  const [editorFiles, setEditorFiles] = useState([
    {
      id: '1',
      name: 'main.js',
      path: '/main.js',
      content: currentCode,
      language: 'javascript',
      isDirty: false,
      isReadOnly: false,
      lastModified: new Date()
    }
  ]);

  const {
    isRunning,
    logs,
    diffContent,
    transformedCode,
    runTransformation,
    stopTransformation,
    clearLogs
  } = useCodeTransformation();

  const { addToHistory } = useTransformationHistory();

  const handleRunLoop = async () => {
    try {
      await runTransformation(currentCode);
      toast({
        title: "Transformation Complete",
        description: "Your code has been analyzed and optimized by the dual-AI system.",
      });
    } catch (error) {
      toast({
        title: "Transformation Error",
        description: "An error occurred during the transformation process.",
        variant: "destructive",
      });
    }
  };

  const handleApplyChanges = () => {
    if (transformedCode) {
      // Save to history before applying
      addToHistory({
        originalCode: currentCode,
        transformedCode,
        iterations: 5,
        finalScore: 0.95,
        title: `Transformation ${new Date().toLocaleTimeString()}`
      });

      setCurrentCode(transformedCode);
      
      toast({
        title: "Changes Applied",
        description: "The optimized code has been applied to your editor.",
      });

      // Clear diff after applying
      setTimeout(() => {
        clearLogs();
      }, 1000);
    }
  };

  const handleSelectExample = (code: string) => {
    setCurrentCode(code);
    setShowExamples(false);
    toast({
      title: "Example Loaded",
      description: "Code example has been loaded into the editor.",
    });
  };

  const handleDownloadCode = () => {
    const codeToDownload = transformedCode || currentCode;
    const blob = new Blob([codeToDownload], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'optimized-code.js';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Code Downloaded",
      description: "Your code has been saved to your downloads folder.",
    });
  };

  // Enhanced editor handlers
  const handleFileChange = (fileId: string, content: string) => {
    setEditorFiles(prev => prev.map(file =>
      file.id === fileId
        ? { ...file, content, isDirty: true, lastModified: new Date() }
        : file
    ));

    // Update main code if it's the main file
    if (fileId === '1') {
      setCurrentCode(content);
    }
  };

  const handleFileCreate = (name: string, path: string, language: string) => {
    const newFile = {
      id: Date.now().toString(),
      name,
      path,
      content: '',
      language,
      isDirty: false,
      isReadOnly: false,
      lastModified: new Date()
    };
    setEditorFiles(prev => [...prev, newFile]);
  };

  const handleFileDelete = (fileId: string) => {
    setEditorFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const handleFileRename = (fileId: string, newName: string) => {
    setEditorFiles(prev => prev.map(file =>
      file.id === fileId
        ? { ...file, name: newName, isDirty: true }
        : file
    ));
  };

  const handleFileSave = (fileId: string) => {
    setEditorFiles(prev => prev.map(file =>
      file.id === fileId
        ? { ...file, isDirty: false }
        : file
    ));

    toast({
      title: "File Saved",
      description: `${editorFiles.find(f => f.id === fileId)?.name} has been saved.`,
    });
  };

  const handleFileSaveAll = () => {
    setEditorFiles(prev => prev.map(file => ({ ...file, isDirty: false })));

    toast({
      title: "All Files Saved",
      description: "All open files have been saved.",
    });
  };

  // Set up keyboard shortcuts
  useKeyboardShortcuts({
    onRunTransformation: handleRunLoop,
    onStopTransformation: stopTransformation,
    onClearLogs: clearLogs,
    onApplyChanges: handleApplyChanges
  });

  // Show onboarding if needed
  if (showOnboarding) {
    return (
      <div className="min-h-screen bg-background p-4">
        <OnboardingWizard onComplete={() => setShowOnboarding(false)} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-background/95 backdrop-blur-sm" role="banner">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="text-muted-foreground hover:text-foreground"
              aria-label="Go back to home page"
              data-testid="main-back-button"
            >
              <ArrowLeft className="w-4 h-4 mr-2" aria-hidden="true" />
              Back
            </Button>
            <h1 className="text-2xl font-bold text-foreground">Metamorphic Reactor</h1>
            <Badge
              variant="secondary"
              className="bg-agent-planner/20 text-agent-planner-foreground border-agent-planner/30"
              data-testid="dashboard-badge"
            >
              Dashboard
            </Badge>
          </div>
          <nav className="flex items-center space-x-2" role="navigation" aria-label="Dashboard actions">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowExamples(!showExamples)}
              className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              aria-label={showExamples ? "Hide code examples" : "Show code examples"}
              aria-pressed={showExamples}
              data-testid="examples-toggle"
              type="button"
            >
              <BookOpen className="w-4 h-4 mr-2" aria-hidden="true" />
              Examples
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHelp(!showHelp)}
              className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              aria-label={showHelp ? "Hide help panel" : "Show help panel"}
              aria-pressed={showHelp}
              data-testid="help-toggle"
            >
              <Keyboard className="w-4 h-4 mr-2" aria-hidden="true" />
              Help
            </Button>
            {transformedCode && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleApplyChanges}
                className="border-success text-success-foreground hover:bg-success/20"
                aria-label="Apply the transformed code changes to the editor"
                data-testid="apply-changes-button"
              >
                <RotateCcw className="w-4 h-4 mr-2" aria-hidden="true" />
                Apply Changes
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadCode}
              className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              aria-label="Download current code as a file"
              data-testid="download-button"
            >
              <Download className="w-4 h-4 mr-2" aria-hidden="true" />
              Download
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/history')}
              className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              aria-label="View transformation history"
              data-testid="history-button"
            >
              <History className="w-4 h-4 mr-2" aria-hidden="true" />
              History
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/settings')}
              className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              aria-label="Open settings page"
              data-testid="settings-button"
            >
              <Settings className="w-4 h-4 mr-2" aria-hidden="true" />
              Settings
            </Button>
          </nav>
        </div>
      </header>

      {/* Main Content with Tabs */}
      <main className="h-[calc(100vh-73px)]" role="main">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="border-b border-border px-2 sm:px-6">
            <TabsList className="grid w-full max-w-3xl grid-cols-2 sm:grid-cols-3 lg:grid-cols-7 bg-muted/50" role="tablist" aria-label="Dashboard sections">
              <TabsTrigger
                value="code-editor"
                className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2 text-muted-foreground data-[state=active]:text-foreground"
                role="tab"
                aria-controls="code-editor-panel"
                data-testid="tab-code-editor"
              >
                <Code2 className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Editor</span>
              </TabsTrigger>
              <TabsTrigger
                value="enhanced-editor"
                className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2 text-muted-foreground data-[state=active]:text-foreground"
                role="tab"
                aria-controls="enhanced-editor-panel"
                data-testid="tab-enhanced-editor"
              >
                <Code2 className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Enhanced</span>
              </TabsTrigger>
              <TabsTrigger
                value="overview"
                className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2 text-muted-foreground data-[state=active]:text-foreground focus-enhanced"
                role="tab"
                aria-controls="overview-panel"
                data-testid="tab-overview"
              >
                <Monitor className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Overview</span>
              </TabsTrigger>
              <TabsTrigger
                value="analytics"
                className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2 text-muted-foreground data-[state=active]:text-foreground lg:flex focus-enhanced"
                role="tab"
                aria-controls="analytics-panel"
                data-testid="tab-analytics"
              >
                <BarChart3 className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Analytics</span>
              </TabsTrigger>
              <TabsTrigger
                value="admin"
                className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2 text-muted-foreground data-[state=active]:text-foreground lg:flex focus-enhanced"
                role="tab"
                aria-controls="admin-panel"
                data-testid="tab-admin"
              >
                <Shield className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Admin</span>
              </TabsTrigger>
              <TabsTrigger
                value="workspace"
                className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2 text-muted-foreground data-[state=active]:text-foreground lg:flex"
                role="tab"
                aria-controls="workspace-panel"
                data-testid="tab-workspace"
              >
                <Users className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Workspace</span>
              </TabsTrigger>
              <TabsTrigger
                value="notifications"
                className="flex items-center justify-center sm:justify-start space-x-0 sm:space-x-2 text-muted-foreground data-[state=active]:text-foreground lg:flex"
                role="tab"
                aria-controls="notifications-panel"
                data-testid="tab-notifications"
              >
                <Bell className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Alerts</span>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Code Editor Tab */}
          <TabsContent value="code-editor" className="flex-1 m-0" id="code-editor-panel" role="tabpanel" aria-labelledby="code-editor-tab">
            <div className="h-full lg:hidden">
              {/* Mobile Layout - Stacked */}
              <div className="h-full flex flex-col">
                <section className="h-1/2 border-b border-border" aria-labelledby="mobile-code-editor-heading">
                  <div className="flex items-center justify-between p-2 border-b border-border">
                    <h2 id="mobile-code-editor-heading" className="text-sm font-semibold text-foreground">Code Editor</h2>
                    <ControlPanel
                      isRunning={isRunning}
                      onRunLoop={handleRunLoop}
                      onStop={stopTransformation}
                    />
                  </div>
                  <div className="h-[calc(100%-3rem)]">
                    <CodeEditor
                      value={currentCode}
                      onChange={setCurrentCode}
                      language="javascript"
                    />
                  </div>
                </section>
                <section className="h-1/2 flex flex-col" aria-labelledby="mobile-diff-heading">
                  <div className="flex items-center justify-between p-2 border-b border-border">
                    <h2 id="mobile-diff-heading" className="text-sm font-semibold text-foreground">Live Diff</h2>
                    {diffContent && (
                      <Badge className="bg-success/20 text-success-foreground border-success/30 text-xs">
                        Patch Ready
                      </Badge>
                    )}
                  </div>
                  <div className="flex-1">
                    <DiffViewer diffContent={diffContent} />
                  </div>
                </section>
              </div>
            </div>
            <ResizablePanelGroup direction="horizontal" className="h-full hidden lg:flex">
              {/* Left Panel - Code Editor */}
              <ResizablePanel defaultSize={40} minSize={30}>
                <section className="h-full flex flex-col" aria-labelledby="code-editor-heading">
                  <div className="flex items-center justify-between p-4 border-b border-border">
                    <h2 id="code-editor-heading" className="text-lg font-semibold text-foreground">Code Editor</h2>
                    <ControlPanel
                      isRunning={isRunning}
                      onRunLoop={handleRunLoop}
                      onStop={stopTransformation}
                    />
                  </div>
                  <div className="flex-1">
                    <CodeEditor
                      value={currentCode}
                      onChange={setCurrentCode}
                      language="javascript"
                    />
                  </div>
                </section>
              </ResizablePanel>

              <ResizableHandle
                className="w-1 bg-border hover:bg-accent"
                aria-label="Resize panels between code editor and diff viewer"
                aria-orientation="vertical"
              />

              {/* Center Panel - Diff Viewer */}
              <ResizablePanel defaultSize={35} minSize={25}>
                <div className="h-full flex flex-col">
                  <div className="flex items-center justify-between p-4 border-b border-border">
                    <h2 className="text-lg font-semibold text-foreground">Live Diff</h2>
                    {diffContent && (
                      <Badge className="bg-success/20 text-success-foreground border-success/30">
                        Patch Ready
                      </Badge>
                    )}
                  </div>
                  <div className="flex-1">
                    <DiffViewer diffContent={diffContent} />
                  </div>
                </div>
              </ResizablePanel>

              <ResizableHandle
                className="w-1 bg-border hover:bg-accent"
                aria-label="Resize panels between diff viewer and agent logs"
                aria-orientation="vertical"
              />

              {/* Right Panel - Agent Logs & Utilities */}
              <ResizablePanel defaultSize={25} minSize={20}>
                <div className="h-full flex flex-col">
                  <div className="flex items-center justify-between p-4 border-b border-border">
                    <h2 className="text-lg font-semibold text-foreground">
                      {showExamples ? 'Code Examples' : showHelp ? 'Help' : 'Agent Logs'}
                    </h2>
                    {isRunning && !showExamples && !showHelp && (
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-status-running rounded-full animate-pulse"></div>
                        <span className="text-sm text-status-running">Running</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 overflow-hidden">
                    {showExamples ? (
                      <div className="h-full overflow-y-auto">
                        <CodeExamples onSelectExample={handleSelectExample} />
                      </div>
                    ) : showHelp ? (
                      <div className="p-4">
                        <HelpPanel />
                      </div>
                    ) : (
                      <AgentLog logs={logs} />
                    )}
                  </div>
                </div>
              </ResizablePanel>
            </ResizablePanelGroup>
          </TabsContent>

          {/* Enhanced Editor Tab */}
          <TabsContent value="enhanced-editor" className="flex-1 m-0" id="enhanced-editor-panel" role="tabpanel" aria-labelledby="enhanced-editor-tab">
            <EnhancedEditor
              files={editorFiles}
              originalCode={currentCode}
              transformedCode={transformedCode}
              isTransforming={isRunning}
              onFileChange={handleFileChange}
              onFileCreate={handleFileCreate}
              onFileDelete={handleFileDelete}
              onFileRename={handleFileRename}
              onFileSave={handleFileSave}
              onFileSaveAll={handleFileSaveAll}
              onRunTransformation={handleRunLoop}
              onStopTransformation={stopTransformation}
              onApplyChanges={handleApplyChanges}
              onResetChanges={clearLogs}
            />
          </TabsContent>

          {/* Dashboard Integration Tabs */}
          <TabsContent value="overview" className="flex-1 m-0 p-2 sm:p-4 lg:p-6 bg-background" id="overview-panel" role="tabpanel" aria-labelledby="overview-tab">
            <DashboardIntegration />
          </TabsContent>

          <TabsContent value="analytics" className="flex-1 m-0 p-2 sm:p-4 lg:p-6 bg-background" id="analytics-panel" role="tabpanel" aria-labelledby="analytics-tab">
            <DashboardIntegration />
          </TabsContent>

          <TabsContent value="admin" className="flex-1 m-0 p-2 sm:p-4 lg:p-6 bg-background" id="admin-panel" role="tabpanel" aria-labelledby="admin-tab">
            <DashboardIntegration />
          </TabsContent>

          <TabsContent value="workspace" className="flex-1 m-0 p-2 sm:p-4 lg:p-6 bg-background" id="workspace-panel" role="tabpanel" aria-labelledby="workspace-tab">
            <DashboardIntegration />
          </TabsContent>

          <TabsContent value="notifications" className="flex-1 m-0 p-2 sm:p-4 lg:p-6 bg-background" id="notifications-panel" role="tabpanel" aria-labelledby="notifications-tab">
            <DashboardIntegration />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default Dashboard;
