{"metadata": {"generated_at": "2025-06-19T00:00:00Z", "generator": "Memory MCP Source Catalog System", "project": "Code Alchemy Reactor", "total_files": 87, "total_symbols": 20, "total_entities": 150, "total_relations": 85}, "source_files": [{"name": "src/App.tsx", "type": "source_file", "observations": ["Main React App component", "TypeScript React file", "Entry point component"], "symbols": ["queryClient", "App"], "imports": ["src/components/ui/toaster.tsx", "src/components/ui/sonner.tsx", "src/components/ui/tooltip.tsx", "src/pages/Index.tsx", "src/pages/Dashboard.tsx", "src/pages/Settings.tsx", "src/pages/History.tsx", "src/pages/NotFound.tsx"], "authors": ["gpt-engineer-app[bot]"], "lines": 34}, {"name": "src/main.tsx", "type": "source_file", "observations": ["Application entry point", "React app initialization", "TypeScript React entry file"], "symbols": [], "imports": ["src/App.tsx", "src/index.css"], "authors": [], "lines": 6}, {"name": "src/lib/utils.ts", "type": "source_file", "observations": ["Utility functions library", "Common helper functions", "TypeScript utility file"], "symbols": ["cn"], "imports": [], "authors": [], "lines": 7}, {"name": "src/services/aiService.ts", "type": "source_file", "observations": ["AI service module", "API integration for AI functionality", "TypeScript service file"], "symbols": ["AgentResponse", "TransformationResult", "AIService", "SCORE_THRESHOLD", "MAX_ITERATIONS", "transformCode", "callPlannerAgent"], "imports": [], "authors": [], "lines": 235}, {"name": "src/pages/Dashboard.tsx", "type": "source_file", "observations": ["Dashboard page component", "Main dashboard interface", "TypeScript React page"], "symbols": ["Dashboard", "handleRunLoop", "handleApplyChanges", "handleSelectExample", "handleDownloadCode", "handleFileChange", "handleFileCreate", "handleFileDelete", "handleFileRename", "handleFileSave"], "imports": ["src/components/ui/button.tsx", "src/components/ui/badge.tsx", "src/components/ui/tabs.tsx", "src/components/ui/resizable.tsx", "src/hooks/use-toast.ts", "src/components/CodeEditor.tsx", "src/components/DiffViewer.tsx", "src/components/AgentLog.tsx", "src/components/editor/EnhancedEditor.tsx", "src/components/ControlPanel.tsx", "src/components/CodeExamples.tsx", "src/components/HelpPanel.tsx", "src/hooks/useCodeTransformation.ts", "src/hooks/useTransformationHistory.ts", "src/hooks/useKeyboardShortcuts.ts", "src/components/dashboard/DashboardIntegration.tsx", "src/components/app/onboarding/OnboardingWizard.tsx"], "authors": ["gpt-engineer-app[bot]", "mike"], "lines": 563}], "symbols": [{"name": "queryClient", "type": "symbol", "entity_type": "constant", "file": "src/App.tsx", "line": 13, "description": "QueryClient instance for React Query"}, {"name": "App", "type": "symbol", "entity_type": "component", "file": "src/App.tsx", "lines": "15-31", "description": "Main React application component with routing"}, {"name": "cn", "type": "symbol", "entity_type": "function", "file": "src/lib/utils.ts", "lines": "4-6", "description": "Utility function combining clsx and tailwind-merge"}, {"name": "AIService", "type": "symbol", "entity_type": "class", "file": "src/services/aiService.ts", "lines": "18-234", "description": "Main AI service class with dual-agent system"}, {"name": "Dashboard", "type": "symbol", "entity_type": "component", "file": "src/pages/Dashboard.tsx", "lines": "25-560", "description": "Main dashboard page component with complex state management"}], "authors": [{"name": "gpt-engineer-app[bot]", "type": "person", "contributions": ["src/App.tsx", "src/pages/Dashboard.tsx"], "role": "Primary contributor (bot)"}, {"name": "mike", "type": "person", "contributions": ["src/pages/Dashboard.tsx"], "role": "Secondary contributor (human)"}], "statistics": {"file_types": {"tsx": 75, "ts": 10, "css": 2}, "component_categories": {"pages": 5, "ui_components": 40, "app_components": 15, "editor_components": 5, "hooks": 5, "services": 1, "stories": 3, "tests": 3}, "lines_of_code": {"total_estimated": 5000, "largest_file": "src/pages/Dashboard.tsx (563 lines)", "smallest_file": "src/main.tsx (6 lines)"}}}