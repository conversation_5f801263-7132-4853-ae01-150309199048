<testsuites id="" name="" tests="6" failures="6" skipped="0" errors="0" time="39.462137000000006">
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:43:53.962Z" hostname="chromium" tests="1" failures="1" skipped="0" time="31.627" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="31.627">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: Should have no critical accessibility violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      48 |
      49 |     // Expect no critical or serious violations
    > 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);
         |                                                                                    ^
      51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);
      52 |     
      53 |     // Target score ≥ 97
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:50:84

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Accessibility violations: [33m2[39m
Violations details:
1. aria-valid-attr-value: Ensure all ARIA attributes have valid values
   Impact: critical
   Help: ARIA attributes must conform to valid values
   Nodes: 2
2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
   Impact: serious
   Help: Elements must meet minimum color contrast ratio thresholds
   Nodes: 15
Accessibility Score: 85/100
Critical: 1, Serious: 1, Moderate: 0, Minor: 0

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:43:53.962Z" hostname="firefox" tests="1" failures="1" skipped="0" time="0.017" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="0.017">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [firefox] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1487\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:43:53.962Z" hostname="webkit" tests="1" failures="1" skipped="0" time="0.018" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="0.018">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [webkit] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2182\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:43:53.962Z" hostname="Mobile Chrome" tests="1" failures="1" skipped="0" time="27.251" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="27.251">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [Mobile Chrome] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: Should have no critical accessibility violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      48 |
      49 |     // Expect no critical or serious violations
    > 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);
         |                                                                                    ^
      51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);
      52 |     
      53 |     // Target score ≥ 97
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:50:84

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Accessibility violations: [33m2[39m
Violations details:
1. button-name: Ensure buttons have discernible text
   Impact: critical
   Help: Buttons must have discernible text
   Nodes: 2
2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
   Impact: serious
   Help: Elements must meet minimum color contrast ratio thresholds
   Nodes: 9
Accessibility Score: 85/100
Critical: 1, Serious: 1, Moderate: 0, Minor: 0

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:43:53.962Z" hostname="Mobile Safari" tests="1" failures="1" skipped="0" time="0.141" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="0.141">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [Mobile Safari] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2182\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-19T20:43:53.962Z" hostname="Tablet" tests="1" failures="1" skipped="0" time="12.211" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="12.211">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [Tablet] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: Should have no critical accessibility violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      48 |
      49 |     // Expect no critical or serious violations
    > 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);
         |                                                                                    ^
      51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);
      52 |     
      53 |     // Target score ≥ 97
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:50:84

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Accessibility violations: [33m2[39m
Violations details:
1. aria-valid-attr-value: Ensure all ARIA attributes have valid values
   Impact: critical
   Help: ARIA attributes must conform to valid values
   Nodes: 2
2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
   Impact: serious
   Help: Elements must meet minimum color contrast ratio thresholds
   Nodes: 15
Accessibility Score: 85/100
Critical: 1, Serious: 1, Moderate: 0, Minor: 0

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>