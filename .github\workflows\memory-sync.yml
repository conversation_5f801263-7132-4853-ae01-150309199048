name: Memory MCP Sync Check

on:
  pull_request:
    paths:
      - 'src/**'
  push:
    branches:
      - main
    paths:
      - 'src/**'

jobs:
  memory-sync-check:
    runs-on: ubuntu-latest
    name: Verify Source Files in Memory MCP
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@v40
        with:
          files: |
            src/**/*.ts
            src/**/*.tsx
            src/**/*.js
            src/**/*.jsx
            src/**/*.css

      - name: Check Memory MCP Sync
        if: steps.changed-files.outputs.any_changed == 'true'
        run: |
          echo "🔍 Checking Memory MCP sync for changed files..."
          echo "Changed files: ${{ steps.changed-files.outputs.all_changed_files }}"
          
          # Create a simple Node.js script to check memory sync
          cat > check-memory-sync.js << 'EOF'
          const fs = require('fs');
          const path = require('path');
          
          // Read the memory dump
          const memoryDumpPath = 'artifacts/src_memory_dump.json';
          if (!fs.existsSync(memoryDumpPath)) {
            console.error('❌ Memory dump not found. Run source catalog generation first.');
            process.exit(1);
          }
          
          const memoryDump = JSON.parse(fs.readFileSync(memoryDumpPath, 'utf8'));
          const catalogedFiles = memoryDump.source_files.map(f => f.name);
          
          // Get all source files
          const getAllFiles = (dir, fileList = []) => {
            const files = fs.readdirSync(dir);
            files.forEach(file => {
              const filePath = path.join(dir, file);
              if (fs.statSync(filePath).isDirectory()) {
                getAllFiles(filePath, fileList);
              } else if (/\.(ts|tsx|js|jsx|css)$/.test(file)) {
                fileList.push(filePath.replace(/\\/g, '/'));
              }
            });
            return fileList;
          };
          
          const allSourceFiles = getAllFiles('src');
          const missingFiles = allSourceFiles.filter(file => !catalogedFiles.includes(file));
          
          console.log(`📊 Total source files: ${allSourceFiles.length}`);
          console.log(`📚 Cataloged files: ${catalogedFiles.length}`);
          console.log(`❓ Missing files: ${missingFiles.length}`);
          
          if (missingFiles.length > 0) {
            console.error('\n❌ The following files are not in Memory MCP:');
            missingFiles.forEach(file => console.error(`  - ${file}`));
            console.error('\n💡 Please run the source catalog generation to update Memory MCP.');
            process.exit(1);
          }
          
          // Check if memory dump is recent (within last 24 hours)
          const dumpDate = new Date(memoryDump.metadata.generated_at);
          const now = new Date();
          const hoursDiff = (now - dumpDate) / (1000 * 60 * 60);
          
          if (hoursDiff > 24) {
            console.warn(`⚠️  Memory dump is ${Math.round(hoursDiff)} hours old. Consider regenerating.`);
          }
          
          console.log('✅ All source files are cataloged in Memory MCP!');
          console.log(`📈 Coverage: ${((catalogedFiles.length / allSourceFiles.length) * 100).toFixed(1)}%`);
          EOF
          
          node check-memory-sync.js

      - name: Validate Memory Dump Structure
        run: |
          echo "🔍 Validating memory dump structure..."
          
          cat > validate-memory-dump.js << 'EOF'
          const fs = require('fs');
          
          const memoryDumpPath = 'artifacts/src_memory_dump.json';
          if (!fs.existsSync(memoryDumpPath)) {
            console.log('ℹ️  Memory dump not found, skipping validation.');
            process.exit(0);
          }
          
          try {
            const memoryDump = JSON.parse(fs.readFileSync(memoryDumpPath, 'utf8'));
            
            // Validate required structure
            const requiredFields = ['metadata', 'source_files', 'symbols', 'authors', 'statistics'];
            const missingFields = requiredFields.filter(field => !memoryDump[field]);
            
            if (missingFields.length > 0) {
              console.error(`❌ Missing required fields: ${missingFields.join(', ')}`);
              process.exit(1);
            }
            
            // Validate metadata
            if (!memoryDump.metadata.generated_at || !memoryDump.metadata.total_files) {
              console.error('❌ Invalid metadata structure');
              process.exit(1);
            }
            
            // Validate source files have required fields
            const invalidFiles = memoryDump.source_files.filter(file => 
              !file.name || !file.type || !file.observations
            );
            
            if (invalidFiles.length > 0) {
              console.error(`❌ ${invalidFiles.length} files have invalid structure`);
              process.exit(1);
            }
            
            console.log('✅ Memory dump structure is valid!');
            console.log(`📊 Files: ${memoryDump.source_files.length}, Symbols: ${memoryDump.symbols.length}`);
            
          } catch (error) {
            console.error('❌ Failed to parse memory dump:', error.message);
            process.exit(1);
          }
          EOF
          
          node validate-memory-dump.js

      - name: Comment on PR
        if: github.event_name == 'pull_request' && steps.changed-files.outputs.any_changed == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            
            let comment = '## 🧠 Memory MCP Sync Check\n\n';
            
            if (fs.existsSync('artifacts/src_memory_dump.json')) {
              const memoryDump = JSON.parse(fs.readFileSync('artifacts/src_memory_dump.json', 'utf8'));
              comment += `✅ **Memory sync verified!**\n\n`;
              comment += `📊 **Statistics:**\n`;
              comment += `- Total files cataloged: ${memoryDump.source_files.length}\n`;
              comment += `- Total symbols: ${memoryDump.symbols.length}\n`;
              comment += `- Last updated: ${memoryDump.metadata.generated_at}\n\n`;
              comment += `🔗 [View full catalog](./docs/src_catalog/README.md)`;
            } else {
              comment += `⚠️ **Memory dump not found**\n\n`;
              comment += `Please run the source catalog generation to create the memory dump.`;
            }
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

      - name: Upload Memory Dump Artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: memory-dump
          path: artifacts/src_memory_dump.json
          retention-days: 30
