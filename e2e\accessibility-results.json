{"accessibility": {"Mobile": {"score": 85, "violations": {"critical": 1, "serious": 1, "moderate": 0, "minor": 0, "total": 2}, "details": [{"id": "button-name", "impact": "critical", "description": "Ensure buttons have discernible text", "help": "Buttons must have discernible text", "nodes": 2}, {"id": "color-contrast", "impact": "serious", "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "nodes": 4}]}, "Tablet": {"score": 85, "violations": {"critical": 1, "serious": 1, "moderate": 0, "minor": 0, "total": 2}, "details": [{"id": "aria-valid-attr-value", "impact": "critical", "description": "Ensure all ARIA attributes have valid values", "help": "ARIA attributes must conform to valid values", "nodes": 2}, {"id": "color-contrast", "impact": "serious", "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "nodes": 11}]}, "Desktop": {"score": 85, "violations": {"critical": 1, "serious": 1, "moderate": 0, "minor": 0, "total": 2}, "details": [{"id": "aria-valid-attr-value", "impact": "critical", "description": "Ensure all ARIA attributes have valid values", "help": "ARIA attributes must conform to valid values", "nodes": 2}, {"id": "color-contrast", "impact": "serious", "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "nodes": 10}]}}, "screenshots": [{"breakpoint": "Mobile", "dimensions": "320x568", "path": "e2e/screenshots/accessibility-mobile-320x568.png"}, {"breakpoint": "Tablet", "dimensions": "768x1024", "path": "e2e/screenshots/accessibility-tablet-768x1024.png"}, {"breakpoint": "Desktop", "dimensions": "1280x720", "path": "e2e/screenshots/accessibility-desktop-1280x720.png"}], "timestamp": "2025-06-19T20:49:09.953Z", "focusTests": [{"buttonIndex": 0, "focused": true, "visibleFocus": true}, {"buttonIndex": 1, "focused": true, "visibleFocus": true}, {"buttonIndex": 2, "focused": true, "visibleFocus": true}, {"buttonIndex": 3, "focused": true, "visibleFocus": true}, {"buttonIndex": 4, "focused": true, "visibleFocus": true}]}