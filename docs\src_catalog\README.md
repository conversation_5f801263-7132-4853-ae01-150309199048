# Source Code Catalog

This document provides a comprehensive overview of all source files in the `/src` directory of the Code Alchemy Reactor project.

## Overview

- **Total Files**: 87
- **Total Symbols**: 20+ (key symbols documented)
- **Primary Authors**: gpt-engineer-app[bot], mike
- **Languages**: TypeScript, CSS, JavaScript

## File Summary Table

| File | Type | Size (Lines) | Symbols | Author | Description |
|------|------|-------------|---------|---------|-------------|
| `src/App.tsx` | React Component | 34 | 2 | gpt-engineer-app[bot] | Main application component with routing |
| `src/main.tsx` | Entry Point | 6 | 0 | - | React app initialization |
| `src/lib/utils.ts` | Utility | 7 | 1 | - | Common utility functions |
| `src/services/aiService.ts` | Service | 235 | 7 | - | AI service with dual-agent system |
| `src/pages/Dashboard.tsx` | Page Component | 563 | 10 | gpt-engineer-app[bot], mike | Main dashboard interface |
| `src/pages/History.tsx` | Page Component | - | - | - | Transformation history view |
| `src/pages/Index.tsx` | Page Component | - | - | - | Landing/home page |
| `src/pages/Settings.tsx` | Page Component | - | - | - | Application settings |
| `src/pages/NotFound.tsx` | Page Component | - | - | - | 404 error page |

## Component Categories

### 📱 **Pages** (5 files)
- Dashboard, History, Index, Settings, NotFound
- Main application routes and views

### 🧩 **Components** (50+ files)
- **Core Components**: AgentLog, CodeEditor, DiffViewer, ControlPanel
- **Editor Components**: EnhancedEditor, MultiFileEditor, SearchReplace
- **App Components**: Onboarding, Queue, Billing, Admin modules
- **UI Components**: 40+ shadcn/ui components (Button, Card, Dialog, etc.)

### 🎣 **Hooks** (5 files)
- Custom React hooks for code transformation, keyboard shortcuts, mobile detection

### 🔧 **Services** (1 file)
- AI service with dual-agent architecture

### 📚 **Stories** (3 files)
- Storybook documentation for components

### 🧪 **Tests** (3 files)
- Accessibility, responsive design, and streaming tests

## Key Symbols

### Core Application
- **App**: Main React component with routing setup
- **queryClient**: React Query client instance

### AI Service
- **AIService**: Main class for dual-agent code transformation
- **AgentResponse**: Interface for agent responses
- **TransformationResult**: Interface for transformation results

### Dashboard Functions
- **handleRunLoop**: Executes code transformation
- **handleApplyChanges**: Applies transformed code
- **handleFileChange**: Manages file content changes

## Import Graph

```mermaid
graph TD
    A[main.tsx] --> B[App.tsx]
    B --> C[pages/Dashboard.tsx]
    B --> D[pages/Index.tsx]
    B --> E[pages/Settings.tsx]
    B --> F[pages/History.tsx]
    B --> G[pages/NotFound.tsx]
    
    C --> H[components/CodeEditor.tsx]
    C --> I[components/DiffViewer.tsx]
    C --> J[components/AgentLog.tsx]
    C --> K[hooks/useCodeTransformation.ts]
    
    H --> L[lib/utils.ts]
    I --> L
    J --> L
```

## Architecture Overview

The codebase follows a modern React architecture with:

1. **Component-Based Design**: Modular UI components using shadcn/ui
2. **Custom Hooks**: Reusable logic for transformations and UI state
3. **Service Layer**: AI service for backend integration
4. **Type Safety**: Full TypeScript implementation
5. **Testing**: Comprehensive test coverage for accessibility and functionality

## File Details

For detailed information about individual files, see:
- [App.tsx](./App.md) - Main application component
- [Dashboard.tsx](./Dashboard.md) - Dashboard page component
- [aiService.ts](./aiService.md) - AI service implementation

## Development Notes

- All components use TypeScript for type safety
- UI components follow shadcn/ui design system
- Custom hooks provide reusable business logic
- Comprehensive test coverage for critical functionality
- Storybook documentation for component development

---

*Generated by Memory MCP Source Catalog System*
*Last Updated: 2025-06-19*
